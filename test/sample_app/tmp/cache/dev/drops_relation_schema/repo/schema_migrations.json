{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": true, "nullable": true}, "name": ["atom", "version"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "source": ["atom", "schema_migrations"], "foreign_keys": [], "indices": {"attributes": {"indices": []}, "__struct__": "Indices"}, "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": true, "nullable": true}, "name": ["atom", "version"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}