{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": true, "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "index_name": "posts_title_index", "check_constraints": [], "primary_key": false, "nullable": false}, "name": ["atom", "title"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "body"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "integer"], "foreign_key": false, "index_name": "posts_published_index", "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "published"], "type": ["atom", "boolean"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 0, "index": false, "type": ["atom", "integer"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "view_count"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "foreign_key": true, "index_name": "posts_user_id_index", "check_constraints": [], "primary_key": false, "nullable": false}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": false}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": false}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "source": ["atom", "posts"], "foreign_keys": [{"attributes": {"field": ["atom", "user_id"], "references_field": ["atom", "id"], "references_table": ["atom", "users"]}, "__struct__": "ForeignKey"}], "indices": {"attributes": {"indices": [{"attributes": {"name": ["atom", "posts_title_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "index_name": "posts_title_index", "check_constraints": [], "primary_key": false, "nullable": false}, "name": ["atom", "title"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "posts_published_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "integer"], "foreign_key": false, "index_name": "posts_published_index", "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "published"], "type": ["atom", "boolean"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "posts_user_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "foreign_key": true, "index_name": "posts_user_id_index", "check_constraints": [], "primary_key": false, "nullable": false}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}]}, "__struct__": "Indices"}, "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": true, "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}