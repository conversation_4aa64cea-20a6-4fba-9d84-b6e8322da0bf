{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": true, "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "index_name": "users_email_index", "check_constraints": [], "primary_key": false, "nullable": false}, "name": ["atom", "email"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "index_name": "users_last_name_first_name_index", "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "index_name": "users_last_name_first_name_index", "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "age"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": true, "index": true, "type": ["atom", "integer"], "foreign_key": false, "index_name": "users_is_active_index", "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "is_active"], "type": ["atom", "boolean"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "profile_data"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": "[]", "index": false, "type": ["atom", "string"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "tags"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "decimal"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "score"], "type": ["atom", "decimal"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "birth_date"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "last_login_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": false}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": false, "nullable": false}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "source": ["atom", "users"], "foreign_keys": [], "indices": {"attributes": {"indices": [{"attributes": {"name": ["atom", "users_is_active_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": true, "index": true, "type": ["atom", "integer"], "foreign_key": false, "index_name": "users_is_active_index", "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "is_active"], "type": ["atom", "boolean"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_last_name_first_name_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "index_name": "users_last_name_first_name_index", "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "index_name": "users_last_name_first_name_index", "check_constraints": [], "primary_key": false, "nullable": true}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_email_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "foreign_key": false, "index_name": "users_email_index", "check_constraints": [], "primary_key": false, "nullable": false}, "name": ["atom", "email"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "unique": true}, "__struct__": "Index"}]}, "__struct__": "Indices"}, "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "foreign_key": false, "index_name": null, "check_constraints": [], "primary_key": true, "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}