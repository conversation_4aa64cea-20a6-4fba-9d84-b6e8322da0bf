{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "primary_key": true, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "nullable": false, "check_constraints": [], "foreign_key": false, "index_name": "users_email_index"}, "name": ["atom", "email"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": "users_last_name_first_name_index"}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": "users_last_name_first_name_index"}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": null}, "name": ["atom", "age"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": true, "index": true, "type": ["atom", "integer"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": "users_is_active_index"}, "name": ["atom", "is_active"], "type": ["atom", "boolean"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": null}, "name": ["atom", "profile_data"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": "[]", "index": false, "type": ["atom", "string"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": null}, "name": ["atom", "tags"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "decimal"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": null}, "name": ["atom", "score"], "type": ["atom", "decimal"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": null}, "name": ["atom", "birth_date"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": null}, "name": ["atom", "last_login_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "nullable": false, "check_constraints": [], "foreign_key": false, "index_name": null}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "nullable": false, "check_constraints": [], "foreign_key": false, "index_name": null}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "source": ["atom", "users"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "primary_key": true, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": null}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": null}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": {"attributes": {"indices": [{"attributes": {"name": ["atom", "users_is_active_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": true, "index": true, "type": ["atom", "integer"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": "users_is_active_index"}, "name": ["atom", "is_active"], "type": ["atom", "boolean"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_last_name_first_name_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": "users_last_name_first_name_index"}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "nullable": true, "check_constraints": [], "foreign_key": false, "index_name": "users_last_name_first_name_index"}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "users_email_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "string"], "primary_key": false, "nullable": false, "check_constraints": [], "foreign_key": false, "index_name": "users_email_index"}, "name": ["atom", "email"], "type": ["atom", "string"], "source": null}, "__struct__": "Field"}], "unique": true}, "__struct__": "Index"}]}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}